version: 1
backend:
  phases:
    # preBuild:
    #   commands:
    #     - rm -rf ~/.amplify
    #     - npm i -g @aws-amplify/cli@latest
    build:
      commands:
        - nvm use 18
        - npm --version
        # - npm install npm@latest -g
        - npm install npm@8.19.3 -g
        - amplifyPush --simple
frontend:
  phases:
    preBuild:
      commands:
        - npm ci
    build:
      commands:
        - npm run build
  artifacts:
    baseDirectory: dist
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
