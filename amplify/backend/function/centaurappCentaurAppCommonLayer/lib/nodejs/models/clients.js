const mongoose = require("mongoose")

const DEFAULTS = {
    ftp_use_sub_folders: false,
    ftp_sub_folders: {
        "FIELDS": "mr_fields",
        "FORM": "mr_form",
        "RESULTS - COLLATED": "mr_results",
        "RESULTS - RACE BY RACE": "mr_results",
        "GEAR CHANGES": "mr_gear_changes",
        "SCRATCHINGS": "mr_scratchings",
    },
    meeting_type_perms: {
        tab: true,
        non_tab: true
    },
    include_ra_ids: false
}

const RACING_AUSTRALIA_TIERS = {
    A: 'Above $100m in turnover',
    B: 'Between $100m and $50m turnover',
    C: 'Between $50m and $25m turnover',
    D: 'Between $25m and $10m turnover',
    E: 'Between $10m and $2m turnover',
    F: 'Below $2m turnover',
    G: 'Personal Use'
}

const MEDIALITY_RACING_TIERS = {
    '1': 'Tier 1',
    '2': 'Tier 2',
    'P': 'Personal Use',
    'W': 'Wholesale'
}

const clientsSchema = new mongoose.Schema({
    _id: String,
    account_holder: String,
    email: String,
    company: String,
    status: Boolean,
    chargebee_customer_id :String, 
    client_type: String,
    racing_australia: {
        enabled: {type: Boolean, default: false},
        price_model: {type: String, enum: ['W', 'R'], default: null},
        pricing_tier: {type: String, enum: Object.keys(RACING_AUSTRALIA_TIERS), default: null}
    },
    mediality_racing: {
        enabled: {type: Boolean, default: false},
        price_model: {type: String, enum: ['W', 'R'], default: null},
        pricing_tier: {type: String, enum: Object.keys(MEDIALITY_RACING_TIERS), default: null}
    },
    // Original fields
    ftp_username: String,
    ftp_password: String,
    ftp_address: String,
    ftp_use_sub_folders: {type: Boolean, default: DEFAULTS.ftp_use_sub_folders},
    ftp_sub_folders: {type: Object, default: DEFAULTS.ftp_sub_folders},
    api_key: String,
    api_key_id: String,
    region_perms: Array,
    files_access: Array,
    fields_access: Array,
    classifications: Array,
    stage_perms: Array,
    is_soft_delete: Boolean,
    meeting_type_perms: {type: Object, default: DEFAULTS.meeting_type_perms},
    include_ra_ids: {type: Boolean, default: DEFAULTS.include_ra_ids}
})

clientsSchema.virtual('client_type_codes').get(function() {
    const types = [];
    
    if (this.racing_australia?.enabled && this.racing_australia.price_model) {
        types.push('RA' + this.racing_australia.price_model);
    }
    
    if (this.mediality_racing?.enabled && this.mediality_racing.price_model) {
        types.push('M' + this.mediality_racing.price_model);
    }
    
    return types;
});

// Virtual to get full descriptions
clientsSchema.virtual('client_types_description').get(function() {
    const descriptions = [];
    
    if (this.racing_australia?.enabled && this.racing_australia.price_model) {
        const model = this.racing_australia.price_model === 'W' ? 'Wholesale' : 'Retail';
        descriptions.push(`Racing Australia, ${model}`);
        
        if (this.racing_australia.pricing_tier) {
            descriptions.push(`RA Tier: ${this.racing_australia.pricing_tier} - ${RACING_AUSTRALIA_TIERS[this.racing_australia.pricing_tier]}`);
        }
    }
    
    if (this.mediality_racing?.enabled && this.mediality_racing.price_model) {
        const model = this.mediality_racing.price_model === 'W' ? 'Wholesale' : 'Retail';
        descriptions.push(`Mediality Racing, ${model}`);
        
        if (this.mediality_racing.pricing_tier) {
            descriptions.push(`MR Tier: ${this.mediality_racing.pricing_tier} - ${MEDIALITY_RACING_TIERS[this.mediality_racing.pricing_tier]}`);
        }
    }
    
    return descriptions;
});


exports.clients = mongoose.model("clients", clientsSchema)