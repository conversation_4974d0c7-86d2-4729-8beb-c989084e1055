<template>
  <Dialog
    v-model="editedItem"
    v-model:visible="isVisible"
    :style="{ width: '1200px', 'max-height': '97%' }"
    header="Client Details"
    :modal="true"
    :closeOnEscape="true"
    @hide="closeForm()"
    class="p-fluid client-form"
  >
    <form @submit.prevent="handleSubmit(!v$.$invalid)" class="p-fluid">
      <!-- <span>valid {{!v$.$invalid}} </span> -->
      <div class="formgrid grid">
        <div class="row">
          <div class="col">
            <div class="field col">
              <label
                for="account_holder"
                :class="{ 'p-error': v$.editedItem.account_holder.$invalid }"
                >Account Holder <sup class="star-marker">*</sup></label
              >
              <InputText
                id="account_holder"
                v-model="editedItem.account_holder"
                required="true"
                autofocus
                :class="{
                  'p-invalid':
                    v$.editedItem.account_holder.$invalid && submitted,
                }"
              />
              <small
                v-if="
                  v$.editedItem.account_holder.$invalid ||
                  v$.editedItem.account_holder.$pending.$response
                "
                class="p-error"
                >{{
                  v$.editedItem.account_holder.required.$message.replace(
                    "Value",
                    "Account holder"
                  )
                }}</small
              >
            </div>
            <div class="field col">
              <label
                for="email"
                :class="{ 'p-error': v$.editedItem.email.$invalid }"
                >Email <sup class="star-marker">*</sup></label
              >
              <InputText
                id="email"
                v-model="editedItem.email"
                required="true"
                :class="{
                  'p-invalid': v$.editedItem.email.$invalid && submitted,
                }"
              />
              <span v-if="v$.editedItem.email.$error && submitted">
                <span
                  id="email-error"
                  v-for="(error, index) of v$.editedItem.email.$errors"
                  :key="index"
                >
                  <small class="p-error">{{ error.$message }}</small>
                </span>
              </span>
              <small
                v-else-if="
                  (v$.editedItem.email.$invalid && submitted) ||
                  v$.editedItem.email.$pending.$response
                "
                class="p-error"
                >{{
                  v$.editedItem.email.required.$message.replace(
                    "Value",
                    "Email"
                  )
                }}</small
              >
            </div>
            <div class="field col">
              <label for="company">Company</label>
              <InputText
                id="company"
                v-model="editedItem.company"
                required="true"
              />
            </div>
            <div class="col-12">
              <h3>Client Data Sources</h3>
              <div class="grid">
                <!-- Racing Australia Section -->
                <div class="col-6 p-2">
                  <div class="p-card">
                    <div class="p-card-title">Racing Australia</div>
                    <div class="p-card-body">
                      <div class="field-checkbox mb-3">
                        <Checkbox
                          id="ra_enabled"
                          v-model="editedItem.racing_australia.enabled"
                          :binary="true"
                        />
                        <label for="ra_enabled">Enable Racing Australia</label>
                      </div>

                      <div v-if="editedItem.racing_australia.enabled">
                        <div class="field mb-3">
                          <label for="ra_price_model">Price Model</label>
                          <Dropdown
                            id="ra_price_model"
                            v-model="editedItem.racing_australia.price_model"
                            :options="priceModels"
                            optionLabel="name"
                            optionValue="code"
                            placeholder="Select Price Model"
                          />
                        </div>

                        <div class="field">
                          <label for="ra_pricing_tier">Pricing Tier</label>
                          <Dropdown
                            id="ra_pricing_tier"
                            v-model="editedItem.racing_australia.pricing_tier"
                            :options="filteredRacingAustraliaTiers"
                            optionLabel="name"
                            optionValue="code"
                            placeholder="Select Pricing Tier"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Mediality Racing Section -->
                <div class="col-6 p-2">
                  <div class="p-card">
                    <div class="p-card-title">Mediality Racing</div>
                    <div class="p-card-body">
                      <div class="field-checkbox mb-3">
                        <Checkbox
                          id="mr_enabled"
                          v-model="editedItem.mediality_racing.enabled"
                          :binary="true"
                        />
                        <label for="mr_enabled">Enable Mediality Racing</label>
                      </div>

                      <div v-if="editedItem.mediality_racing.enabled">
                        <div class="field mb-3">
                          <label for="mr_price_model">Price Model</label>
                          <Dropdown
                            id="mr_price_model"
                            v-model="editedItem.mediality_racing.price_model"
                            :options="priceModels"
                            optionLabel="name"
                            optionValue="code"
                            placeholder="Select Price Model"
                          />
                        </div>

                        <div class="field">
                          <label for="mr_pricing_tier">Pricing Tier</label>
                          <Dropdown
                            id="mr_pricing_tier"
                            v-model="editedItem.mediality_racing.pricing_tier"
                            :options="medialityRacingTiers"
                            optionLabel="name"
                            optionValue="code"
                            placeholder="Select Pricing Tier"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Display the active client types -->
              <div class="mt-3" v-if="getActiveClientTypes().length > 0">
                <h4>Active Client Types</h4>
                <div class="p-2 border-1 surface-border border-round">
                  <ul class="list-none p-0 m-0">
                    <li
                      v-for="(type, index) in getActiveClientTypes()"
                      :key="index"
                      class="mb-2"
                    >
                      {{ type }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <div class="col" style="border-left: 1px solid grey">
            <div class="field col">
              <label for="ftp_address">FTP Address</label>
              <InputText
                id="ftp_address"
                v-model="editedItem.ftp_address"
                required="true"
              />
            </div>
            <div class="field col">
              <label for="ftp_username">FTP Username</label>
              <InputText
                id="ftp_username"
                v-model="editedItem.ftp_username"
                required="true"
              />
            </div>
            <div class="field col">
              <label for="ftp_password">FTP Password</label>
              <InputText
                id="ftp_password"
                v-model="editedItem.ftp_password"
                required="true"
              />
            </div>
            <div class="field col">
              <label for="chargebee_customer_id">Chargebee Customer ID</label>
              <InputText
                id="chargebee_customer_id"
                v-model="editedItem.chargebee_customer_id"
                required="true"
              />
            </div>
            <div class="field col">
              <label for="client_type">Customer Type</label>
              <Dropdown
                id="client_type"
                v-model="editedItem.client_type"
                :options="customerTypes"
                optionLabel="name"
                optionValue="value"
                placeholder="Select Customer Type"
              />
            </div>
          </div>
          <div class="col" style="border-left: 1px solid grey">
            <div class="field-checkbox col active-section">
              <Checkbox
                id="binary"
                v-model="editedItem.status"
                :binary="true"
              />
              <label for="binary">Active</label>
            </div>
            <div class="field-checkbox col active-section">
              <Checkbox
                id="subFol"
                v-model="editedItem.ftp_use_sub_folders"
                :binary="true"
              />
              <label for="subFol">Use FTP sub folders</label>
            </div>
            <!-- New Meeting Type Permissions Section -->
            <div
              class="field-checkbox col active-section"
              v-if="editedItem.meeting_type_perms"
            >
              <Checkbox
                id="tabMeetings"
                v-model="editedItem.meeting_type_perms.tab"
                :binary="true"
              />
              <label for="tabMeetings">Tab Meetings</label>
            </div>
            <div
              class="field-checkbox col active-section"
              v-if="editedItem.meeting_type_perms"
            >
              <Checkbox
                id="nonTabMeetings"
                v-model="editedItem.meeting_type_perms.non_tab"
                :binary="true"
              />
              <label for="nonTabMeetings">Non-Tab Meetings</label>
            </div>
            <!-- New RA IDs Section -->
            <div class="field-checkbox col active-section">
              <Checkbox
                id="includeRaIds"
                v-model="editedItem.include_ra_ids"
                :binary="true"
              />
              <label for="includeRaIds">Include RA IDs in XML</label>
            </div>
            <!-- <div class="field col">
                  <label for="api_key_id">API Key Id</label>
                  <InputText id="api_key_id" v-model="editedItem.api_key_id" required="true" disabled/>
                </div>
                <div class="field col">
                  <label for="api_key">API Key</label>
                  <InputText id="api_key" v-model="editedItem.api_key" required="true" disabled/>
                </div> -->
          </div>
        </div>
        <div class="row">
          <div class="field col">
            <multiSelect
              title="Files"
              :selectedItems="editedItem.files_access"
              @changedItems="changedFiles"
              :items="files"
            />
          </div>
          <div class="field col">
            <multiSelect
              title="Regions"
              :selectedItems="editedItem.region_perms"
              @changedItems="changedRegions"
              :items="regions"
            />
          </div>
          <div class="field col">
            <multiSelect
              title="Classification"
              :selectedItems="editedItem.classifications"
              @changedItems="changedClassifications"
              :items="classifications"
            />
            <multiSelect
              title="Stage"
              :selectedItems="editedItem.stage_perms"
              @changedItems="changedStages"
              :items="stages"
              style="margin-top: 50px"
            />
          </div>
          <div class="field col">
            <multiSelect
              title="Fields"
              :selectedItems="editedItem.fields_access"
              @changedItems="changedFields"
              :items="fields"
            />
          </div>
        </div>
      </div>
    </form>

    <template #footer>
      <Button
        label="Cancel"
        icon="pi pi-times"
        class="p-button-text"
        @click="closeForm()"
      />
      <Button
        label="Save"
        icon="pi pi-check"
        class="p-button-text"
        @click="saveForm()"
        :disabled="v$.$invalid == true"
      />
    </template>
  </Dialog>
</template>

<script>
import Dialog from "primevue/dialog";
import Button from "primevue/button";
import InputText from "primevue/inputtext";
import Checkbox from "primevue/checkbox";
import { API } from "aws-amplify";
import multiSelect from "./multi-select.vue";
import { useVuelidate } from "@vuelidate/core";
import { required, email } from "@vuelidate/validators";
import Dropdown from "primevue/dropdown";
export default {
  components: {
    Dialog,
    Button,
    InputText,
    Checkbox,
    multiSelect,
    Dropdown,
  },
  setup: () => ({ v$: useVuelidate() }),
  validations() {
    return {
      editedItem: {
        account_holder: {
          required,
        },
        email: {
          required,
          email,
        },
      },
    };
  },
  data: () => ({
    submitted: false,
    editedItem: {},
    isVisible: null,
    files: [
      "FIELDS",
      "FORM",
      "FORM - 2 RUNS",
      "FORM - 10 RUNS",
      "RESULTS - COLLATED",
      "RESULTS - RACE BY RACE",
      "SCRATCHINGS",
      "GEAR CHANGES",
    ],
    regions: [
      "NSW",
      "ACT",
      "QLD",
      "VIC",
      "SA",
      "WA",
      "TAS",
      "NT",
      "NZ",
      "SGP",
      "HK",
    ],
    fields: [
      "GEAR CHANGES",
      "RATINGS",
      "SILKS",
      "BETTING",
      "SELECTIONS",
      "SPEED MAPS",
      "COMMENT",
    ],
    classifications: [
      "UNVALIDATED MEETINGS",
      "METRO",
      "PROVINCIAL",
      "COUNTRY",
      "INTERNATIONAL",
    ],
    stages: ["ACCEPTANCES", "WEIGHTS", "NOMINATIONS"],
    priceModels: [
      { name: "Wholesale", code: "W" },
      { name: "Retail", code: "R" },
    ],
    racingAustraliaTiers: [
      { name: "A - Above $100m in turnover", code: "A" },
      { name: "B - Between $100m and $50m turnover", code: "B" },
      { name: "C - Between $50m and $25m turnover", code: "C" },
      { name: "D - Between $25m and $10m turnover", code: "D" },
      { name: "E - Between $10m and $2m turnover", code: "E" },
      { name: "F - Below $2m turnover", code: "F" },
      { name: "G - Personal Use", code: "G" },
    ],
    medialityRacingTiers: [
      { name: "1 - Tier 1", code: "1" },
      { name: "2 - Tier 2", code: "2" },
      { name: "P - Personal Use", code: "P" },
      { name: "W - Wholesale", code: "W" },
    ],
    customerTypes: [
      { name: "Commercial", value: "Commercial" },
      { name: "Personal", value: "Personal" },
      { name: "Wagering Operator", value: "Wagering Operator" },
      {
        name: "Wagering Operator Associate",
        value: "Wagering Operator Associate",
      },
    ],
  }),
  props: {
    initialItem: {},
    visible: null,
  },
  methods: {
    async saveForm() {
      // Make sure we have valid objects before saving
      const dataToSave = {
        ...this.editedItem,
        racing_australia: {
          enabled: this.editedItem.racing_australia?.enabled || false,
          price_model: this.editedItem.racing_australia?.enabled
            ? this.editedItem.racing_australia.price_model || null
            : null,
          pricing_tier: this.editedItem.racing_australia?.enabled
            ? this.editedItem.racing_australia.pricing_tier || null
            : null,
        },
        mediality_racing: {
          enabled: this.editedItem.mediality_racing?.enabled || false,
          price_model: this.editedItem.mediality_racing?.enabled
            ? this.editedItem.mediality_racing.price_model || null
            : null,
          pricing_tier: this.editedItem.mediality_racing?.enabled
            ? this.editedItem.mediality_racing.pricing_tier || null
            : null,
        },
        ftp_sub_folders: this.editedItem.ftp_sub_folders || {
          FIELDS: "mr_fields",
          FORM: "mr_form",
          "RESULTS - COLLATED": "mr_results",
          "RESULTS - RACE BY RACE": "mr_results",
          "GEAR CHANGES": "mr_gear_changes",
          SCRATCHINGS: "mr_scratchings",
        },
        meeting_type_perms: this.editedItem.meeting_type_perms || {
          tab: true,
          non_tab: false,
        },
        include_ra_ids:
          this.editedItem.include_ra_ids !== undefined
            ? this.editedItem.include_ra_ids
            : false,
      };

      if (this.initialItem._id != null) {
        try {
          await API.put(
            "MrCenApiGateway",
            `/admin/client?id=${this.initialItem._id}`,
            {
              body: dataToSave,
            }
          );
        } catch (error) {
          if (error.response && error.response.status === 409) {
            console.error("Conflict error:", error);
          } else {
            console.error("Error:", error);
          }
        }
      } else {
        await API.post("MrCenApiGateway", `/admin/client`, {
          body: dataToSave,
        });
      }
      this.closeForm();
    },
    closeForm() {
      this.isVisible = false;
      this.$emit("visible", false);
    },
    changedRegions(val) {
      this.editedItem.region_perms = val;
    },
    changedFields(val) {
      this.editedItem.fields_access = val;
    },
    changedFiles(val) {
      this.editedItem.files_access = val;
    },
    changedClassifications(val) {
      this.editedItem.classifications = val;
    },
    changedStages(val) {
      this.editedItem.stage_perms = val;
    },
    handleSubmit(isFormValid) {
      this.submitted = true;

      if (!isFormValid) {
        return;
      }
    },
    getClientTypeCode() {
      if (!this.editedItem.data_source || !this.editedItem.price_model)
        return "";
      return this.editedItem.data_source + this.editedItem.price_model;
    },
    getClientTypeDescription() {
      if (!this.editedItem.data_source || !this.editedItem.price_model)
        return "";

      const sourceDesc =
        this.dataSources.find((s) => s.code === this.editedItem.data_source)
          ?.name || "";
      const priceDesc =
        this.priceModels.find((p) => p.code === this.editedItem.price_model)
          ?.name || "";

      return `${sourceDesc}, ${priceDesc}`;
    },
    resetTierIfNeeded() {
      if (
        this.editedItem.racing_australia?.price_model === "W" &&
        this.editedItem.racing_australia?.pricing_tier === "G"
      ) {
        this.editedItem.racing_australia.pricing_tier = null;
      } else if (
        this.editedItem.racing_australia?.price_model === "R" &&
        this.editedItem.racing_australia?.pricing_tier !== "G"
      ) {
        this.editedItem.racing_australia.pricing_tier = null;
      }
    },
    getActiveClientTypes() {
      const types = [];

      if (
        this.editedItem.racing_australia?.enabled &&
        this.editedItem.racing_australia.price_model
      ) {
        const model =
          this.editedItem.racing_australia.price_model === "W"
            ? "Wholesale"
            : "Retail";
        let description = `Racing Australia, ${model} (RA${this.editedItem.racing_australia.price_model})`;

        if (this.editedItem.racing_australia.pricing_tier) {
          const tier = this.racingAustraliaTiers.find(
            (t) => t.code === this.editedItem.racing_australia.pricing_tier
          );
          description += ` - Tier ${
            this.editedItem.racing_australia.pricing_tier
          }: ${tier ? tier.name.split(" - ")[1] : ""}`;
        }

        types.push(description);
      }

      if (
        this.editedItem.mediality_racing?.enabled &&
        this.editedItem.mediality_racing.price_model
      ) {
        const model =
          this.editedItem.mediality_racing.price_model === "W"
            ? "Wholesale"
            : "Retail";
        let description = `Mediality Racing, ${model} (M${this.editedItem.mediality_racing.price_model})`;

        if (this.editedItem.mediality_racing.pricing_tier) {
          const tier = this.medialityRacingTiers.find(
            (t) => t.code === this.editedItem.mediality_racing.pricing_tier
          );
          description += ` - Tier ${
            this.editedItem.mediality_racing.pricing_tier
          }: ${tier ? tier.name.split(" - ")[1] : ""}`;
        }

        types.push(description);
      }

      return types;
    },
  },
  computed: {
    availableTiers() {
      if (this.editedItem.data_source === "RA") {
        return this.racingAustraliaTiers;
      } else if (this.editedItem.data_source === "M") {
        return this.medialityRacingTiers;
      }
      return [];
    },
    filteredRacingAustraliaTiers() {
      const model = this.editedItem.racing_australia?.price_model;
      if (model === "R") {
        // Retail: only allow personal use
        return this.racingAustraliaTiers.filter((t) => t.code === "G");
      } else if (model === "W") {
        // Wholesale: disallow personal use
        return this.racingAustraliaTiers.filter((t) => t.code !== "G");
      } else {
        // If no price model is selected, show all
        return this.racingAustraliaTiers;
      }
    },
  },
  watch: {
    initialItem(val) {
      // Create a new object with defaults for reactivity
      this.editedItem = {
        ...val,
        racing_australia: val.racing_australia || { enabled: false },
        mediality_racing: val.mediality_racing || { enabled: false },
        meeting_type_perms: val.meeting_type_perms || {
          tab: true,
          non_tab: false,
        },
        include_ra_ids:
          val.include_ra_ids !== undefined ? val.include_ra_ids : false,
        client_type: val.client_type || null,
      };
      console.log(this.editedItem);
    },
    visible(val) {
      this.isVisible = val;
    },
    isVisible(val) {
      this.$emit("visible", val);
    },
    "editedItem.data_source": function (newVal) {
      if (newVal) {
        this.resetTierIfNeeded();
      }
    },
  },
  mounted() {},
};
</script>

<style scoped>
.active-section {
  margin-top: 25px;
}

.section-seperator {
  width: 100%;
  margin: 5px 10px 10px 10px;
  height: 1px;
  border: none;
  background: lightgray;
}

.col-label {
  margin: 5px 15px;
  font-weight: bold;
}

.field > label {
  margin-bottom: 0.1rem;
}

.star-marker {
  font-size: large;
  top: unset;
}
</style>
